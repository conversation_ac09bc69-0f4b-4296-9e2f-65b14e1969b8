<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>收入纳税明细</title>
    <style>
        /* CSS Reset and Basic Styles */
        body, html {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
            background-color: #F8F8F8; /* A slightly off-white background */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Main container to simulate phone screen */
        .container {
            max-width: 600px; /* Typical phone width */
            margin: 0 auto;
            background-color: #F8F8F8;
        }

        /* Header Styles */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            background-color: #FBFBFB;
            border-bottom: 1px solid #EFEFEF;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .header .back-button {
            font-size: 16px;
            color: #333;
            text-decoration: none;
        }
        .header .back-button::before {
            content: '<';
            font-family: sans-serif; /* Use a standard font for the arrow */
            margin-right: 5px;
            font-weight: bold;
            color: #999;
        }
        .header .title {
            font-size: 18px;
            font-weight: 600;
            color: #000;
        }
        .header .action-button {
            font-size: 16px;
            color: #007AFF; /* iOS-like blue */
            text-decoration: none;
        }

        /* Summary Card Styles */
        .summary-card {
            background-color: #fff;
            margin: 15px;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
        }
        .summary-card .income-total {
            display: flex;
            align-items: center;
            font-size: 15px;
            color: #666;
        }
        .summary-card .income-total .question-mark {
            display: inline-block;
            width: 16px;
            height: 16px;
            line-height: 16px;
            text-align: center;
            border: 1px solid #ccc;
            border-radius: 50%;
            color: #ccc;
            font-size: 12px;
            margin-left: 6px;
        }
        .summary-card .income-amount {
            font-size: 32px;
            font-weight: 500;
            color: #000;
            margin: 8px 0;
        }
        .summary-card .tax-total {
            font-size: 15px;
            color: #333;
        }

        /* Tax Records List Styles */
        .tax-records-list {
            margin: 0 15px;
            padding: 0;
            background-color: transparent; /* Remove white background */
            border-radius: 0;
            overflow: visible;
            box-shadow: none;
        }
        .tax-record {
            padding: 15px 20px;
            background-color: #fff;
            margin-bottom: 10px; /* Add gray gap between records */
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
        }
        .tax-record:last-child {
            margin-bottom: 0;
        }
        .tax-record .details {
            flex-grow: 1;
        }
        .tax-record .record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        .tax-record .record-title {
            font-size: 16px;
            font-weight: 500;
            color: #000;
        }
        .tax-record .record-date {
            font-size: 14px;
            color: #999;
            font-weight: normal;
        }
        .tax-record .record-meta {
            font-size: 13px;
            color: #999;
            line-height: 1.5;
            margin-bottom: 8px;
        }
        .tax-record .record-footer {
            font-size: 13px;
            line-height: 1.4;
        }
        .tax-record .record-footer .income {
            color: #999;
            margin-bottom: 2px;
        }
        .tax-record .record-footer .tax {
            color: #999;
        }
        .tax-record .arrow {
            color: #D1D1D6;
            font-size: 16px;
            font-weight: normal;
            margin-left: 15px;
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
        }

    </style>
</head>
<body>

    <div class="container">
        <header class="header">
            <a href="#" class="back-button">返回</a>
            <h1 class="title">收入纳税明细</h1>
            <a href="#" class="action-button">批量申诉</a>
        </header>

        <main>
            <div class="summary-card">
                <div class="income-total">
                    收入合计 <span class="question-mark">?</span>
                </div>
                <div class="income-amount">159872.92元</div>
                <div class="tax-total">已申报税额合计： 6784.36元</div>
            </div>

            <ul class="tax-records-list">
                <li class="tax-record">
                    <div class="details">
                        <div class="record-header">
                            <span class="record-title">工资薪金</span>
                            <span class="record-date">2024-09</span>
                        </div>
                        <div class="record-meta">
                            所得项目小类：正常工资薪金<br>
                            扣缴义务人：广州人饿人力资源服务有...
                        </div>
                        <div class="record-footer">
                            <div class="income">收入：14183.06元</div>
                            <div class="tax">已申报税额：606.54元</div>
                        </div>
                    </div>
                    <div class="arrow">&gt;</div>
                </li>
                <li class="tax-record">
                    <div class="details">
                        <div class="record-header">
                            <span class="record-title">工资薪金</span>
                            <span class="record-date">2024-08</span>
                        </div>
                        <div class="record-meta">
                            所得项目小类：正常工资薪金<br>
                            扣缴义务人：广州人饿人力资源服务有...
                        </div>
                        <div class="record-footer">
                            <div class="income">收入：14183.06元</div>
                            <div class="tax">已申报税额：910.30元</div>
                        </div>
                    </div>
                    <div class="arrow">&gt;</div>
                </li>
                <li class="tax-record">
                    <div class="details">
                        <div class="record-header">
                            <span class="record-title">工资薪金</span>
                            <span class="record-date">2024-07</span>
                        </div>
                        <div class="record-meta">
                            所得项目小类：正常工资薪金<br>
                            扣缴义务人：广州人饿人力资源服务有...
                        </div>
                        <div class="record-footer">
                            <div class="income">收入：14183.06元</div>
                            <div class="tax">已申报税额：896.93元</div>
                        </div>
                    </div>
                    <div class="arrow">&gt;</div>
                </li>
                <li class="tax-record">
                    <div class="details">
                        <div class="record-header">
                            <span class="record-title">工资薪金</span>
                            <span class="record-date">2024-06</span>
                        </div>
                        <div class="record-meta">
                            所得项目小类：正常工资薪金<br>
                            扣缴义务人：广州人饿人力资源服务有...
                        </div>
                        <div class="record-footer">
                            <div class="income">收入：16330.00元</div>
                            <div class="tax">已申报税额：695.94元</div>
                        </div>
                    </div>
                    <div class="arrow">&gt;</div>
                </li>
                <li class="tax-record">
                    <div class="details">
                        <div class="record-header">
                            <span class="record-title">工资薪金</span>
                            <span class="record-date">2024-05</span>
                        </div>
                        <div class="record-meta">
                            所得项目小类：正常工资薪金<br>
                            扣缴义务人：广州人饿人力资源服务有...
                        </div>
                        <div class="record-footer">
                            <div class="income">收入：16330.00元</div>
                            <div class="tax">已申报税额：883.21元</div>
                        </div>
                    </div>
                    <div class="arrow">&gt;</div>
                </li>
                <li class="tax-record">
                    <div class="details">
                        <div class="record-header">
                            <span class="record-title">工资薪金</span>
                            <span class="record-date">2024-04</span>
                        </div>
                        <div class="record-meta">
                            所得项目小类：正常工资薪金<br>
                            扣缴义务人：广州人饿人力资源服务有...
                        </div>
                        <div class="record-footer">
                            <div class="income">收入：16330.00元</div>
                            <div class="tax">已申报税额：268.98元</div>
                        </div>
                    </div>
                    <div class="arrow">&gt;</div>
                </li>
                <li class="tax-record">
                    <div class="details">
                        <div class="record-header">
                            <span class="record-title">工资薪金</span>
                            <span class="record-date">2024-03</span>
                        </div>
                        <div class="record-meta">
                            所得项目小类：正常工资薪金<br>
                            扣缴义务人：广州人饿人力资源服务有...
                        </div>
                        <div class="record-footer">
                            <div class="income">收入：16330.00元</div>
                            <div class="tax">已申报税额：262.87元</div>
                        </div>
                    </div>
                    <div class="arrow">&gt;</div>
                </li>
                 <li class="tax-record">
                    <div class="details">
                        <div class="record-header">
                            <span class="record-title">工资薪金</span>
                            <span class="record-date">2024-02</span>
                        </div>
                        <div class="record-meta">
                            所得项目小类：正常工资薪金<br>
                            扣缴义务人：广州人饿人力资源服务有...
                        </div>
                        <div class="record-footer">
                            <div class="income">收入：16380.00元</div>
                            <div class="tax">已申报税额：276.28元</div>
                        </div>
                    </div>
                    <div class="arrow">&gt;</div>
                </li>
                 <li class="tax-record">
                    <div class="details">
                        <div class="record-header">
                            <span class="record-title">工资薪金</span>
                            <span class="record-date">2024-02</span>
                        </div>
                        <div class="record-meta">
                            所得项目小类：全年一次性奖金收入<br>
                            扣缴义务人：广州人饿人力资源服务有...
                        </div>
                        <div class="record-footer">
                            <div class="income">收入：25000.00元</div>
                            <div class="tax">已申报税额：750.00元</div>
                        </div>
                    </div>
                    <div class="arrow">&gt;</div>
                </li>
                <li class="tax-record">
                    <div class="details">
                        <div class="record-header">
                            <span class="record-title">工资薪金</span>
                            <span class="record-date">2024-01</span>
                        </div>
                        <div class="record-meta">
                            所得项目小类：正常工资薪金<br>
                            扣缴义务人：广州人饿人力资源服务有...
                        </div>
                        <div class="record-footer">
                            <span>收入：16330.00元</span>
                            <span>已申报税额：270.47元</span>
                        </div>
                    </div>
                    <div class="arrow">&gt;</div>
                </li>
            </ul>
        </main>
    </div>

</body>
</html>