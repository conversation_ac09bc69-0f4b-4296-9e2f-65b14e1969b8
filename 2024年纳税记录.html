<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>收入纳税明细</title>
    <style>
        /* CSS Reset and Basic Styles */
        body, html {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
            background-color: #F8F8F8; /* A slightly off-white background */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Main container to simulate phone screen */
        .container {
            max-width: 375px; /* iPhone standard width */
            margin: 0 auto;
            background-color: #F8F8F8;
        }

        /* Header Styles */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            background-color: #FBFBFB;
            border-bottom: 1px solid #EFEFEF;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .header .back-button {
            font-size: 16px;
            color: #007AFF;
            text-decoration: none;
            display: flex;
            align-items: center;
            font-weight: normal;
        }
        .header .back-button::before {
            content: '<';
            font-size: 20px;
            margin-right: 4px;
            font-weight: normal;
        }
        .header .title {
            font-size: 16px;
            font-weight: normal;
            color: #000;
        }
        .header .action-button {
            font-size: 14px;
            color: #007AFF;
            text-decoration: none;
            font-weight: normal;
        }

        /* Summary Card Styles */
        .summary-card {
            background-color: #fff;
            margin: 10px 15px 15px 15px;
            padding: 20px 20px 15px 20px;
            position: relative;
        }
        .summary-card .income-total {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 14px;
            color: #000;
            margin-bottom: 8px;
        }
        .summary-card .income-total-left {
            display: flex;
            align-items: center;
        }
        .summary-card .income-total .question-mark {
            display: inline-block;
            width: 16px;
            height: 16px;
            line-height: 14px;
            text-align: center;
            border: 1px solid #C7C7CC;
            border-radius: 50%;
            color: blue;
            font-size: 11px;
            margin-left: 5px;
        }
        .summary-card .income-total-right {
            font-size: 14px;
            color: #8E8E93;
        }
        .summary-card .divider {
            height: 1px;
            background-color: #E5E5EA;
            margin: 16px 0;
        }
        .summary-card .tax-total {
            font-size: 14px;
            color: #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .summary-card .tax-amount {
            font-size: 14px;
            color: #000;
            font-weight: 400;
        }


        /* Tax Records List Styles */
        .tax-records-list {
            margin: 0 15px;
            padding: 0;
            background-color: transparent;
            border-radius: 0;
            overflow: visible;
            box-shadow: none;
        }
        .tax-record {
            padding: 16px 20px;
            background-color: #fff;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }
        .tax-record:last-child {
            margin-bottom: 0;
        }
        .tax-record .details {
            flex-grow: 1;
        }
        .tax-record .record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;
        }
        .tax-record .record-title {
            font-size: 16px;
            font-weight: normal;
            color: #000;
        }
        .tax-record .record-date {
            font-size: 16px;
            color: #000;
            font-weight: normal;
        }
        .tax-record .record-meta {
            font-size: 13px;
            color: #999CA5;
            line-height: 1.4;
            margin-bottom: 4px;
        }
        .tax-record .record-footer {
            font-size: 13px;
            line-height: 1.3;
        }
        .tax-record .record-footer .income {
            color: #999CA5;
            margin-bottom: 1px;
        }
        .tax-record .record-footer .tax {
            color: #999CA5;
        }
        .tax-record .arrow {
            color: #C7C7CC;
            font-size: 26px;
            font-weight: normal;
            margin-left: 15px;
            margin-top: 8px;
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            align-self: center;
        }

    </style>
</head>
<body>

    <div class="container">
        <header class="header">
            <a href="#" class="back-button">返回</a>
            <h1 class="title">收入纳税明细</h1>
            <a href="#" class="action-button">批量申诉</a>
        </header>

        <main>
            <div class="summary-card">
                <div class="income-total">
                    <div class="income-total-left">
                        收入合计 <span class="question-mark">?</span>:
                    </div>
                    <div class="income-total-right">
                        159872.92元
                    </div>
                </div>
                <div class="divider"></div>
                <div class="tax-total">
                    <span>已申报税额合计：</span>
                    <span class="tax-amount">5784.36元</span>
                </div>
            </div>

            <ul class="tax-records-list">
                <li class="tax-record">
                    <div class="details">
                        <div class="record-header">
                            <span class="record-title">工资薪金</span>
                            <span class="record-date">2024-09</span>
                        </div>
                        <div class="record-meta">
                            所得项目小类：正常工资薪金<br>
                            扣缴义务人：广州人饿人力资源服务有...
                        </div>
                        <div class="record-footer">
                            <div class="income">收入：14183.06元</div>
                            <div class="tax">已申报税额：606.54元</div>
                        </div>
                    </div>
                    <div class="arrow">&gt;</div>
                </li>
                <li class="tax-record">
                    <div class="details">
                        <div class="record-header">
                            <span class="record-title">工资薪金</span>
                            <span class="record-date">2024-08</span>
                        </div>
                        <div class="record-meta">
                            所得项目小类：正常工资薪金<br>
                            扣缴义务人：广州人饿人力资源服务有...
                        </div>
                        <div class="record-footer">
                            <div class="income">收入：14183.06元</div>
                            <div class="tax">已申报税额：910.30元</div>
                        </div>
                    </div>
                    <div class="arrow">&gt;</div>
                </li>
                <li class="tax-record">
                    <div class="details">
                        <div class="record-header">
                            <span class="record-title">工资薪金</span>
                            <span class="record-date">2024-07</span>
                        </div>
                        <div class="record-meta">
                            所得项目小类：正常工资薪金<br>
                            扣缴义务人：广州人饿人力资源服务有...
                        </div>
                        <div class="record-footer">
                            <div class="income">收入：14183.06元</div>
                            <div class="tax">已申报税额：896.93元</div>
                        </div>
                    </div>
                    <div class="arrow">&gt;</div>
                </li>
                <li class="tax-record">
                    <div class="details">
                        <div class="record-header">
                            <span class="record-title">工资薪金</span>
                            <span class="record-date">2024-06</span>
                        </div>
                        <div class="record-meta">
                            所得项目小类：正常工资薪金<br>
                            扣缴义务人：广州人饿人力资源服务有...
                        </div>
                        <div class="record-footer">
                            <div class="income">收入：16330.00元</div>
                            <div class="tax">已申报税额：695.94元</div>
                        </div>
                    </div>
                    <div class="arrow">&gt;</div>
                </li>
                <li class="tax-record">
                    <div class="details">
                        <div class="record-header">
                            <span class="record-title">工资薪金</span>
                            <span class="record-date">2024-05</span>
                        </div>
                        <div class="record-meta">
                            所得项目小类：正常工资薪金<br>
                            扣缴义务人：广州人饿人力资源服务有...
                        </div>
                        <div class="record-footer">
                            <div class="income">收入：16330.00元</div>
                            <div class="tax">已申报税额：883.21元</div>
                        </div>
                    </div>
                    <div class="arrow">&gt;</div>
                </li>
                <li class="tax-record">
                    <div class="details">
                        <div class="record-header">
                            <span class="record-title">工资薪金</span>
                            <span class="record-date">2024-04</span>
                        </div>
                        <div class="record-meta">
                            所得项目小类：正常工资薪金<br>
                            扣缴义务人：广州人饿人力资源服务有...
                        </div>
                        <div class="record-footer">
                            <div class="income">收入：16330.00元</div>
                            <div class="tax">已申报税额：268.98元</div>
                        </div>
                    </div>
                    <div class="arrow">&gt;</div>
                </li>
                <li class="tax-record">
                    <div class="details">
                        <div class="record-header">
                            <span class="record-title">工资薪金</span>
                            <span class="record-date">2024-03</span>
                        </div>
                        <div class="record-meta">
                            所得项目小类：正常工资薪金<br>
                            扣缴义务人：广州人饿人力资源服务有...
                        </div>
                        <div class="record-footer">
                            <div class="income">收入：16330.00元</div>
                            <div class="tax">已申报税额：262.87元</div>
                        </div>
                    </div>
                    <div class="arrow">&gt;</div>
                </li>
                 <li class="tax-record">
                    <div class="details">
                        <div class="record-header">
                            <span class="record-title">工资薪金</span>
                            <span class="record-date">2024-02</span>
                        </div>
                        <div class="record-meta">
                            所得项目小类：正常工资薪金<br>
                            扣缴义务人：广州人饿人力资源服务有...
                        </div>
                        <div class="record-footer">
                            <div class="income">收入：16380.00元</div>
                            <div class="tax">已申报税额：276.28元</div>
                        </div>
                    </div>
                    <div class="arrow">&gt;</div>
                </li>
                 <li class="tax-record">
                    <div class="details">
                        <div class="record-header">
                            <span class="record-title">工资薪金</span>
                            <span class="record-date">2024-02</span>
                        </div>
                        <div class="record-meta">
                            所得项目小类：全年一次性奖金收入<br>
                            扣缴义务人：广州人饿人力资源服务有...
                        </div>
                        <div class="record-footer">
                            <div class="income">收入：25000.00元</div>
                            <div class="tax">已申报税额：750.00元</div>
                        </div>
                    </div>
                    <div class="arrow">&gt;</div>
                </li>
                <li class="tax-record">
                    <div class="details">
                        <div class="record-header">
                            <span class="record-title">工资薪金</span>
                            <span class="record-date">2024-01</span>
                        </div>
                        <div class="record-meta">
                            所得项目小类：正常工资薪金<br>
                            扣缴义务人：广州人饿人力资源服务有...
                        </div>
                        <div class="record-footer">
                            <div class="income">收入：16330.00元</div>
                            <div class="tax">已申报税额：270.47元</div>
                        </div>
                    </div>
                    <div class="arrow">&gt;</div>
                </li>
            </ul>
        </main>
    </div>

</body>
</html>